# StoryWeaver 项目文档索引

> 本文件为项目文档的完整索引，便于工具和开发者快速查找和调用相关文档

**最后更新**: 2025-07-12
**文档总数**: 80+个
**文档根目录**: `./docs/`

## 🆕 文档整理更新 (2025-07-12)

- ✅ 创建了新的报告分类系统
- ✅ 将根目录的报告文件按类型分类整理
- ✅ 创建了配置文件专用目录
- ✅ 设立了临时文件管理目录
- ✅ 更新了文档索引结构

## � 项目结构变更记录 (2025-07-12)

### 重大变更：frontend文件夹删除
- **变更时间**: 2025年7月12日
- **变更类型**: 目录结构重构
- **影响范围**: 前端开发和部署流程

#### 变更详情
- ❌ **已删除**: `frontend/` 目录（开发版本前端）
- ✅ **保留**: `frontend-production/` 目录（生产版本前端）
- 🎯 **新策略**: 统一使用frontend-production作为唯一前端目录

#### 变更影响
- **开发流程**: 所有前端开发工作转移到frontend-production目录
- **部署流程**: 使用frontend-production目录的部署脚本
- **文档引用**: 需要更新所有指向frontend/的文档路径
- **配置文件**: frontend-production包含完整的配置文件

#### 变更优势
- ✅ **简化维护**: 只需维护一个前端版本
- ✅ **避免混淆**: 消除开发版本和生产版本的差异
- ✅ **提高效率**: 减少重复的配置和部署工作
- ✅ **降低风险**: 避免调试代码意外进入生产环境

## �📋 完整文档清单

### 📊 项目报告中心 (`./docs/reports/`)

| 分类 | 路径 | 描述 |
|------|------|------|
| 项目进度报告 | `./docs/reports/progress/` | 项目整体进度和状态更新 |
| 修复报告 | `./docs/reports/fixes/` | 问题修复和系统优化报告 |
| 功能实现报告 | `./docs/reports/implementation/` | 新功能开发和技术集成报告 |
| 测试报告 | `./docs/reports/testing/` | 功能测试和质量保证报告 |

### 🔧 配置文件中心 (`./docs/config/`)

| 文件类型 | 路径 | 描述 |
|----------|------|------|
| API配置指南 | `./docs/config/API指南.txt` | API服务配置说明 |
| Google AI配置 | `./docs/config/Gemini-api.txt` | Google AI服务配置 |
| OAuth配置 | `./docs/config/Google-oauth-key.txt` | Google OAuth认证配置 |
| 支付配置 | `./docs/config/stripe.txt` | Stripe支付系统配置 |
| 测试密钥说明 | `./docs/config/测试所需key.txt` | 开发测试所需的密钥说明 |

### 🏠 核心项目文档 (`./docs/core/`)

| 文件名 | 路径 | 描述 |
|--------|------|------|
| README.md | `./docs/core/README.md` | 项目完整技术文档 |
| 项目介绍.md | `./docs/core/项目介绍.md` | 项目概念和愿景 |
| 项目状态总结_2025-01-02.md | `./docs/core/项目状态总结_2025-01-02.md` | 最新项目状态 |
| FEATURE_IMPLEMENTATION_STATUS_ANALYSIS.md | `./docs/core/FEATURE_IMPLEMENTATION_STATUS_ANALYSIS.md` | 功能实现状态分析报告 |
| 项目进度更新_2025-01-04.md | `./docs/core/项目进度更新_2025-01-04.md` | 基于ACE分析的项目进度重大更新 |
| CODE_OF_CONDUCT.md | `./docs/core/CODE_OF_CONDUCT.md` | 行为准则 |
| CONTRIBUTING.md | `./docs/core/CONTRIBUTING.md` | 贡献指南 |
| SECURITY.md | `./docs/core/SECURITY.md` | 安全指南 |

### 🏗️ 架构设计文档 (`./docs/architecture/`)

| 文件名 | 路径 | 描述 |
|--------|------|------|
| StoryWeaver_完整项目计划.md | `./docs/architecture/StoryWeaver_完整项目计划.md` | 项目整体规划 |
| StoryWeaver_DurableObjects_实施计划.md | `./docs/architecture/StoryWeaver_DurableObjects_实施计划.md` | 实时功能架构 |
| technical_implementation.md | `./docs/architecture/technical_implementation.md` | 技术实现方案 |
| website_framework.md | `./docs/architecture/website_framework.md` | 网站框架设计 |
| ui_ux_design_guide.md | `./docs/architecture/ui_ux_design_guide.md` | UI/UX设计指南 |

### 👨‍💻 开发指南 (`./docs/development/`)

| 文件名 | 路径 | 描述 |
|--------|------|------|
| DEVELOPMENT_GUIDE.md | `./docs/development/DEVELOPMENT_GUIDE.md` | 基础开发指南 |
| DEVELOPMENT_GUIDE_PART2.md | `./docs/development/DEVELOPMENT_GUIDE_PART2.md` | 高级开发技巧 |
| DEVELOPMENT_GUIDE_PART3.md | `./docs/development/DEVELOPMENT_GUIDE_PART3.md` | 测试和调试 |
| DEVELOPMENT_GUIDE_PART4.md | `./docs/development/DEVELOPMENT_GUIDE_PART4.md` | 部署和维护 |
| FRONTEND_DEVELOPMENT_GUIDE.md | `./docs/development/FRONTEND_DEVELOPMENT_GUIDE.md` | 前端开发指南 |
| FRONTEND_DEVELOPMENT_GUIDE_DUPLICATE.md | `./docs/development/FRONTEND_DEVELOPMENT_GUIDE_DUPLICATE.md` | 前端开发指南（重复文档） |
| DurableObjects_快速开始指南.md | `./docs/development/DurableObjects_快速开始指南.md` | 实时功能开发 |
| PERFORMANCE_GUIDE.md | `./docs/development/PERFORMANCE_GUIDE.md` | 性能优化指南 |

### 🚀 部署文档 (`./docs/deployment/`)

| 文件名 | 路径 | 描述 |
|--------|------|------|
| DEPLOYMENT_GUIDE.md | `./docs/deployment/DEPLOYMENT_GUIDE.md` | 基础部署指南 |
| PRODUCTION_DEPLOYMENT_GUIDE.md | `./docs/deployment/PRODUCTION_DEPLOYMENT_GUIDE.md` | 生产环境部署 |
| PRODUCTION_DEPLOYMENT_REPORT.md | `./docs/deployment/PRODUCTION_DEPLOYMENT_REPORT.md` | 部署状态报告 |
| PRODUCTION_FRONTEND_CREATION_COMPLETE.md | `./docs/deployment/PRODUCTION_FRONTEND_CREATION_COMPLETE.md` | 生产版本前端创建完成报告 |

### 🔌 API文档 (`./docs/api/`)

| 文件名 | 路径 | 描述 |
|--------|------|------|
| README.md | `./docs/api/README.md` | 后端API概览 |
| API_DOCUMENTATION.md | `./docs/api/API_DOCUMENTATION.md` | 完整API文档 |
| ENVIRONMENT_VARIABLES.md | `./docs/api/ENVIRONMENT_VARIABLES.md` | 环境变量配置 |
| DEPLOYMENT.md | `./docs/api/DEPLOYMENT.md` | 后端部署指南 |
| TESTING.md | `./docs/api/TESTING.md` | API测试指南 |

### 🎨 前端文档 (`./docs/frontend/` ⚠️ 待处理)

> **注意**: 此目录包含原frontend/目录的文档，需要根据frontend-production目录进行更新

| 文件名 | 路径 | 状态 | 描述 |
|--------|------|------|------|
| README.md | `./docs/frontend/README.md` | ⚠️ 需更新 | 前端项目概览 |
| README-StoryWeaver.md | `./docs/frontend/README-StoryWeaver.md` | ⚠️ 需更新 | StoryWeaver前端说明 |
| BUILD-GUIDE.md | `./docs/frontend/BUILD-GUIDE.md` | ⚠️ 需更新 | 前端构建指南 |
| DEPLOYMENT-FULL.md | `./docs/frontend/DEPLOYMENT-FULL.md` | ⚠️ 需更新 | 前端部署指南 |
| STRIPE_INTEGRATION.md | `./docs/frontend/STRIPE_INTEGRATION.md` | ✅ 通用 | Stripe支付集成 |
| STRIPE_401_FIX_GUIDE.md | `./docs/frontend/STRIPE_401_FIX_GUIDE.md` | ✅ 通用 | Stripe 401错误修复 |
| STRIPE_PAYMENT_FIXES_COMPLETE_REPORT.md | `./docs/frontend/STRIPE_PAYMENT_FIXES_COMPLETE_REPORT.md` | ✅ 通用 | 支付修复完整报告 |
| STRIPE_PAYMENT_IMPLEMENTATION.md | `./docs/frontend/STRIPE_PAYMENT_IMPLEMENTATION.md` | ✅ 通用 | 支付系统实现 |
| STRIPE_PAYMENT_TEST_REPORT.md | `./docs/frontend/STRIPE_PAYMENT_TEST_REPORT.md` | ✅ 通用 | 支付测试报告 |
| STRIPE_PAYMENT_TEST_REPORT_FINAL.md | `./docs/frontend/STRIPE_PAYMENT_TEST_REPORT_FINAL.md` | ✅ 通用 | 最终支付测试报告 |
| test-stripe-payment.md | `./docs/frontend/test-stripe-payment.md` | ✅ 通用 | 支付功能测试 |

### 🎯 当前前端目录 (`./frontend-production/`)

| 组件 | 路径 | 描述 |
|------|------|------|
| 源代码 | `./frontend-production/src/` | React应用源代码 |
| 构建输出 | `./frontend-production/dist/` | 生产构建文件 |
| 部署脚本 | `./frontend-production/deploy*.sh` | 各种部署脚本 |
| 配置文件 | `./frontend-production/*.config.*` | 项目配置文件 |
| 环境变量 | `./frontend-production/.env.*` | 环境配置文件 |
| 测试脚本 | `./frontend-production/test-*.sh` | API和功能测试脚本 |

### 📁 历史归档 (`./docs/archive/fixes/`)

| 文件名 | 路径 | 描述 |
|--------|------|------|
| DATA_CONSISTENCY_FIX.md | `./docs/archive/fixes/DATA_CONSISTENCY_FIX.md` | 数据一致性修复 |
| DEBUG_USER_SWITCH_FIX_COMPLETE.md | `./docs/archive/fixes/DEBUG_USER_SWITCH_FIX_COMPLETE.md` | 调试用户切换修复 |
| OAUTH_FIX_GUIDE.md | `./docs/archive/fixes/OAUTH_FIX_GUIDE.md` | OAuth修复指南 |
| PAYMENT_CRITICAL_FIXES_COMPLETE.md | `./docs/archive/fixes/PAYMENT_CRITICAL_FIXES_COMPLETE.md` | 支付关键修复完成 |
| PAYMENT_DUPLICATE_VARIABLE_FIX.md | `./docs/archive/fixes/PAYMENT_DUPLICATE_VARIABLE_FIX.md` | 支付重复变量修复 |
| PAYMENT_FLOW_CRITICAL_FIXES_COMPLETE.md | `./docs/archive/fixes/PAYMENT_FLOW_CRITICAL_FIXES_COMPLETE.md` | 支付流程关键修复 |
| PAYMENT_MODAL_REDIRECT_FIX.md | `./docs/archive/fixes/PAYMENT_MODAL_REDIRECT_FIX.md` | 支付模态框重定向修复 |
| PAYMENT_ROUTING_FIX_COMPLETE.md | `./docs/archive/fixes/PAYMENT_ROUTING_FIX_COMPLETE.md` | 支付路由修复完成 |
| PAYMENT_SUBSCRIPTION_ERROR_FIX.md | `./docs/archive/fixes/PAYMENT_SUBSCRIPTION_ERROR_FIX.md` | 支付订阅错误修复 |
| PAYMENT_SUBSCRIPTION_FIX_COMPLETE.md | `./docs/archive/fixes/PAYMENT_SUBSCRIPTION_FIX_COMPLETE.md` | 支付订阅修复完成 |
| PAYMENT_SYSTEM_FIXES_DEPLOYED.md | `./docs/archive/fixes/PAYMENT_SYSTEM_FIXES_DEPLOYED.md` | 支付系统修复部署 |
| PRODUCTION_DEBUG_USER_CRITICAL_FIX.md | `./docs/archive/fixes/PRODUCTION_DEBUG_USER_CRITICAL_FIX.md` | 生产调试用户关键修复 |
| PRODUCTION_DEBUG_USER_FINAL_FIX.md | `./docs/archive/fixes/PRODUCTION_DEBUG_USER_FINAL_FIX.md` | 生产调试用户最终修复 |
| PRODUCTION_DEBUG_USER_FIX.md | `./docs/archive/fixes/PRODUCTION_DEBUG_USER_FIX.md` | 生产调试用户修复 |
| STRIPE_400_ERROR_FIX.md | `./docs/archive/fixes/STRIPE_400_ERROR_FIX.md` | Stripe 400错误修复 |
| STRIPE_401_FIX_SUMMARY.md | `./docs/archive/fixes/STRIPE_401_FIX_SUMMARY.md` | Stripe 401修复总结 |
| STRIPE_AUTH_FIX_COMPLETE.md | `./docs/archive/fixes/STRIPE_AUTH_FIX_COMPLETE.md` | Stripe认证修复完成 |
| STRIPE_CRITICAL_ERRORS_FIX.md | `./docs/archive/fixes/STRIPE_CRITICAL_ERRORS_FIX.md` | Stripe关键错误修复 |
| STRIPE_PRICE_CONVERSION_FIX.md | `./docs/archive/fixes/STRIPE_PRICE_CONVERSION_FIX.md` | Stripe价格转换修复 |
| STRIPE_PRICE_ID_FIX_COMPLETE.md | `./docs/archive/fixes/STRIPE_PRICE_ID_FIX_COMPLETE.md` | Stripe价格ID修复完成 |
| SUBSCRIPTION_UI_FIXES_COMPLETE.md | `./docs/archive/fixes/SUBSCRIPTION_UI_FIXES_COMPLETE.md` | 订阅UI修复完成 |
| TYPESCRIPT_COMPILATION_FIX.md | `./docs/archive/fixes/TYPESCRIPT_COMPILATION_FIX.md` | TypeScript编译修复 |
| INFINITE_REFRESH_LOOP_FIX_COMPLETE.md | `./docs/archive/fixes/INFINITE_REFRESH_LOOP_FIX_COMPLETE.md` | 无限刷新循环问题修复完成 |
| PAYMENT_500_ERROR_CRITICAL_FIX.md | `./docs/archive/fixes/PAYMENT_500_ERROR_CRITICAL_FIX.md` | 支付500错误关键修复 |
| PAYMENT_VERIFICATION_DEBUG_FIX_COMPLETE.md | `./docs/archive/fixes/PAYMENT_VERIFICATION_DEBUG_FIX_COMPLETE.md` | 支付验证调试修复完成 |

### 📚 文档管理 (`./docs/`)

| 文件名 | 路径 | 描述 |
|--------|------|------|
| README.md | `./docs/README.md` | 文档导航中心 |

### 📁 归档管理 (`./docs/archive/`)

| 文件名 | 路径 | 描述 |
|--------|------|------|
| DOCUMENTATION_ARCHIVE_REPORT.md | `./docs/archive/DOCUMENTATION_ARCHIVE_REPORT.md` | 文档归档报告 |

### 🗂️ 根目录索引文件

| 文件名 | 路径 | 描述 |
|--------|------|------|
| DOCS_INDEX.md | `./DOCS_INDEX.md` | 完整文档索引（本文件） |
| docs-index.json | `./docs-index.json` | JSON格式文档索引（便于工具调用） |

### 📁 临时文件管理 (`./temp/`)

| 文件类型 | 描述 |
|----------|------|
| 测试脚本 | 各种功能测试和API测试脚本 |
| 部署脚本 | 临时的部署和配置脚本 |
| 实验代码 | 功能验证和概念验证代码 |
| 调试工具 | 开发过程中的调试和诊断工具 |

> ⚠️ **注意**: temp/ 目录中的文件仅用于开发测试，不应用于生产环境

## 🔍 快速查找指南

### 按用户类型查找

**项目经理/产品经理**:
- 项目进度: `./docs/reports/progress/` - 查看最新项目状态
- 功能实现: `./docs/reports/implementation/` - 了解功能开发进展
- 问题修复: `./docs/reports/fixes/` - 查看问题解决情况

**新手开发者**:
- 开始: `./docs/core/README.md`
- 项目介绍: `./docs/core/项目介绍.md`
- 开发环境: `./docs/development/DEVELOPMENT_GUIDE.md`
- 配置指南: `./docs/config/` - 查看各种配置说明

**前端开发者**:
- 当前前端目录: `./frontend-production/` - 唯一的前端开发目录
- 前端概览: `./docs/frontend/README.md` (⚠️ 需要根据frontend-production更新)
- 构建指南: `./docs/frontend/BUILD-GUIDE.md` (⚠️ 需要根据frontend-production更新)
- 开发指南: `./docs/development/FRONTEND_DEVELOPMENT_GUIDE.md`
- 部署脚本: `./frontend-production/deploy*.sh` - 各种部署选项

**后端开发者**:
- API概览: `./docs/api/README.md`
- API文档: `./docs/api/API_DOCUMENTATION.md`
- 环境配置: `./docs/api/ENVIRONMENT_VARIABLES.md`
- API配置: `./docs/config/API指南.txt`

**运维人员**:
- 部署指南: `./docs/deployment/DEPLOYMENT_GUIDE.md`
- 生产部署: `./docs/deployment/PRODUCTION_DEPLOYMENT_GUIDE.md`
- 测试报告: `./docs/reports/testing/` - 查看系统测试结果

**架构师**:
- 项目计划: `./docs/architecture/StoryWeaver_完整项目计划.md`
- 技术实现: `./docs/architecture/technical_implementation.md`

**QA测试人员**:
- 测试报告: `./docs/reports/testing/` - 查看测试结果和质量报告
- 修复验证: `./docs/reports/fixes/` - 查看问题修复验证

### 按功能查找

**项目状态和进度**:
- 最新进度: `./docs/reports/progress/` - 查看项目最新状态
- 历史进展: `./docs/reports/progress/` - 查看项目发展历程

**问题排查和修复**:
- 修复记录: `./docs/reports/fixes/` - 查看已解决的问题
- 认证问题: `./docs/reports/fixes/` - OAuth和登录相关修复
- 性能问题: `./docs/reports/fixes/` - 系统性能优化记录

**功能开发**:
- 实现报告: `./docs/reports/implementation/` - 查看功能开发详情
- 音频功能: `./docs/reports/implementation/` - TTS和音频相关实现
- 订阅功能: `./docs/reports/implementation/` - 付费订阅功能实现

**支付系统**:
- 集成指南: `./docs/frontend/STRIPE_INTEGRATION.md`
- 实现文档: `./docs/frontend/STRIPE_PAYMENT_IMPLEMENTATION.md`
- 测试指南: `./docs/frontend/test-stripe-payment.md`
- 配置说明: `./docs/config/stripe.txt`

**实时功能**:
- 实施计划: `./docs/architecture/StoryWeaver_DurableObjects_实施计划.md`
- 快速开始: `./docs/development/DurableObjects_快速开始指南.md`
- WebSocket修复: `./docs/reports/fixes/` - 实时通信相关修复

**AI集成**:
- API指南: `./docs/config/API指南.txt`
- Google AI配置: `./docs/config/Gemini-api.txt`
- 实现报告: `./docs/reports/implementation/` - AI功能实现详情

**测试和质量保证**:
- 测试报告: `./docs/reports/testing/` - 功能和性能测试结果
- 手动测试: `./docs/reports/testing/` - 手动验证测试记录

**性能优化**:
- 优化指南: `./docs/development/PERFORMANCE_GUIDE.md`
- 性能修复: `./docs/reports/fixes/` - 性能问题修复记录

## 🛠️ 工具调用示例

### 命令行查找
```bash
# 查找所有文档
find ./docs -name "*.md" -type f

# 查找特定主题
grep -r "支付" ./docs/
grep -r "API" ./docs/
grep -r "部署" ./docs/
```

### 编程调用
```javascript
// 文档路径映射
const DOCS_MAP = {
  core: './docs/core/',
  architecture: './docs/architecture/',
  development: './docs/development/',
  deployment: './docs/deployment/',
  api: './docs/api/',
  frontend: './docs/frontend/',
  archive: './docs/archive/fixes/'
};

// 获取特定类型的文档
function getDocsByType(type) {
  return DOCS_MAP[type];
}
```

## 📝 维护说明

### 文档分类规则

1. **项目报告** (`./docs/reports/`):
   - **进度报告**: 项目整体状态、里程碑、阶段总结
   - **修复报告**: 问题修复、bug解决、系统优化
   - **实现报告**: 新功能开发、特性实现、技术集成
   - **测试报告**: 功能测试、性能测试、质量保证

2. **配置文件** (`./docs/config/`):
   - API配置说明、密钥管理指南、环境设置文档

3. **临时文件** (`./temp/`):
   - 测试脚本、实验代码、调试工具

### 文档管理流程

1. **新增文档**:
   - 根据内容类型放入相应目录
   - 使用规范的命名格式
   - 更新相应目录的README文件
   - 更新本索引文件

2. **修改文档**:
   - 修改后更新文档的最后修改时间
   - 如有重大变更，更新索引描述

3. **删除文档**:
   - 从目录中删除文件
   - 从索引中移除对应条目
   - 更新相关的README文件

4. **重命名文档**:
   - 更新索引中的路径信息
   - 检查其他文档中的引用链接

### 定期维护任务

- 每月清理 `temp/` 目录中的过时文件
- 每季度检查文档链接的有效性
- 及时归档过时的报告文件
- 保持索引文件的准确性和完整性

## 📈 修改历史追踪

### 2025-07-13 - API端点和个人资料功能修复
- **变更类型**: 问题修复
- **主要变更**: API端点和个人资料功能修复
- **执行者**: Augment Agent
- **变更报告**: `docs/reports/progress/CHANGE_REPORT_2025-07-13_API_ENDPOINTS_AND_PROFILE_FEATURES_FIX.md`

### 2025-07-13 - 订阅导航路由修复
- **变更类型**: 问题修复
- **主要变更**: 订阅导航路由修复
- **执行者**: Augment Agent
- **变更报告**: `docs/reports/progress/CHANGE_REPORT_2025-07-13_SUBSCRIPTION_NAVIGATION_FIX.md`

### 2025-07-13 - 开发流程指南创建
- **变更类型**: 文档整理
- **主要变更**: 开发流程指南创建
- **执行者**: Augment Agent
- **变更报告**: `docs/reports/progress/CHANGE_REPORT_2025-07-13_WORKFLOW_GUIDE_CREATION.md`

### 2025-07-12 - 项目结构重构
- **变更类型**: 目录结构重构
- **主要变更**: 删除frontend/目录，统一使用frontend-production/
- **影响文档**: docs/frontend/目录需要更新
- **执行者**: Augment Agent
- **变更报告**: `docs/reports/progress/PROJECT_ANALYSIS_PHASE2_2025-07-12.md`

### 2025-07-12 - 文档系统重组
- **变更类型**: 文档管理优化
- **主要变更**: 创建报告分类系统，整理根目录文档
- **新增目录**: docs/reports/, docs/config/, temp/
- **执行者**: Augment Agent
- **变更报告**: `docs/reports/progress/DOCUMENTATION_REORGANIZATION_REPORT_2025-07-12.md`

### 2025-01-04 - 初始文档索引
- **变更类型**: 文档索引创建
- **主要变更**: 建立完整的文档索引系统
- **文档总数**: 69个
- **执行者**: AI Assistant

---

*索引最后更新: 2025-07-13*
*维护者: AI Assistant*
*文档整理版本: v2.4*

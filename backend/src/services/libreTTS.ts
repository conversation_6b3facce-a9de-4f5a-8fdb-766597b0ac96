/**
 * LibreTTS 服务
 * 基于Microsoft Edge TTS的文本转语音服务
 * 替代Google Gemini TTS，降低成本并提供更多声音选择
 */

import { VoiceType } from '../types/api';

// LibreTTS支持的声音映射
const VOICE_MAPPING: Record<VoiceType, string> = {
  gentle_female: 'zh-CN-XiaoxiaoNeural',      // 晓晓 - 温柔女声
  warm_male: 'zh-CN-YunxiNeural',             // 云希 - 温暖男声
  child_friendly: 'zh-CN-XiaoyiNeural',       // 晓伊 - 儿童友好
  storyteller: 'zh-CN-XiaomengNeural',        // 晓梦 - 故事讲述
  dramatic: 'zh-CN-YunyangNeural',            // 云扬 - 戏剧化
  whispering: 'zh-CN-XiaorouNeural'           // 晓柔 - 轻柔声音
};

// 声音选项的中文名称
export const VOICE_NAMES: Record<VoiceType, string> = {
  gentle_female: '晓晓（温柔女声）',
  warm_male: '云希（温暖男声）',
  child_friendly: '晓伊（儿童友好）',
  storyteller: '晓梦（故事讲述）',
  dramatic: '云扬（活泼阳光）',
  whispering: '晓柔（轻柔声音）'
};

// TTS端点信息
interface TTSEndpoint {
  r: string;  // region
  t: string;  // token
}

let endpoint: TTSEndpoint | null = null;
let expiredAt: number | null = null;
let clientId = "76a75279-2ffa-4c3d-8db8-7b47252aa41c";

export class LibreTTSService {
  /**
   * 生成语音
   */
  async generateAudio(text: string, voice: VoiceType, subscription?: any): Promise<string> {
    console.log(`🔊 [LibreTTS] 生成音频: 文本长度=${text.length}, 声音=${voice}`);

    try {
      // 获取对应的Microsoft声音ID
      const voiceName = VOICE_MAPPING[voice] || VOICE_MAPPING.gentle_female;
      console.log(`🎵 [LibreTTS] 使用声音: ${voiceName} (${VOICE_NAMES[voice]})`);

      // 根据订阅级别限制文本长度
      const maxLength = this.getMaxTextLength(subscription);
      const truncatedText = text.length > maxLength ? text.substring(0, maxLength) + '...' : text;

      if (text.length > maxLength) {
        console.log(`⚠️ [LibreTTS] 文本被截断: ${text.length} -> ${maxLength} 字符`);
      }

      // 生成音频
      const audioBuffer = await this.generateTTS(truncatedText, voiceName);

      // 转换为base64格式返回
      const audioBase64 = this.arrayBufferToBase64(audioBuffer);
      const audioDataUrl = `data:audio/mpeg;base64,${audioBase64}`;

      console.log(`✅ [LibreTTS] 音频生成成功: ${audioBuffer.byteLength} bytes`);
      return audioDataUrl;

    } catch (error) {
      console.error('❌ [LibreTTS] 音频生成失败:', error);

      // 生成占位符音频
      console.log('🔄 [LibreTTS] 使用占位符音频');
      return this.generatePlaceholderAudio(text.substring(0, 50));
    }
  }

  /**
   * 核心TTS生成逻辑
   */
  private async generateTTS(text: string, voiceName: string): Promise<ArrayBuffer> {
    // 刷新端点
    await this.refreshEndpoint();

    // 生成SSML
    const ssml = this.generateSsml(text, voiceName, 0, 0);

    // 构建请求URL
    const url = `https://${endpoint!.r}.tts.speech.microsoft.com/cognitiveservices/v1`;

    // 设置请求头
    const headers = {
      "Authorization": endpoint!.t,
      "Content-Type": "application/ssml+xml",
      "X-Microsoft-OutputFormat": "audio-24khz-48kbitrate-mono-mp3",
      "User-Agent": "okhttp/4.5.0",
      "Origin": "https://azure.microsoft.com",
      "Referer": "https://azure.microsoft.com/"
    };

    // 发送请求
    const response = await fetch(url, {
      method: "POST",
      headers: headers,
      body: ssml
    });

    if (!response.ok) {
      throw new Error(`TTS请求失败: ${response.status} ${response.statusText}`);
    }

    return await response.arrayBuffer();
  }

  /**
   * 生成SSML格式的文本
   */
  private generateSsml(text: string, voiceName: string, rate: number, pitch: number): string {
    return `<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="zh-CN">
      <voice name="${voiceName}">
        <prosody rate="${rate >= 0 ? '+' : ''}${rate}%" pitch="${pitch >= 0 ? '+' : ''}${pitch}%">
          ${text}
        </prosody>
      </voice>
    </speak>`;
  }

  /**
   * 根据订阅级别获取最大文本长度
   */
  private getMaxTextLength(subscription?: any): number {
    if (!subscription || subscription.status !== 'active') {
      return 500; // 免费用户
    }

    switch (subscription.plan) {
      case 'pro_monthly':
      case 'pro_yearly':
        return 1000; // Pro用户
      case 'unlimited_monthly':
        return 2000; // 无限用户
      default:
        return 500;
    }
  }

  /**
   * 刷新TTS端点
   */
  private async refreshEndpoint(): Promise<void> {
    if (!expiredAt || Date.now() / 1000 > expiredAt - 60) {
      try {
        endpoint = await this.getEndpoint();

        // 解析JWT token获取过期时间
        const parts = endpoint.t.split(".");
        if (parts.length >= 2) {
          const base64 = parts[1].replace(/-/g, '+').replace(/_/g, '/');
          const jsonPayload = decodeURIComponent(
            atob(base64)
              .split('')
              .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
              .join('')
          );
          const decodedJwt = JSON.parse(jsonPayload);
          expiredAt = decodedJwt.exp;
        } else {
          expiredAt = (Date.now() / 1000) + 3600; // 默认1小时过期
        }

        clientId = crypto.randomUUID ? crypto.randomUUID().replace(/-/g, "") : Math.random().toString(36).substring(2, 15);
        console.log(`🔄 [LibreTTS] 获取端点成功, 过期时间剩余: ${((expiredAt - Date.now() / 1000) / 60).toFixed(2)} 分钟`);

      } catch (error) {
        console.error("❌ [LibreTTS] 无法获取端点:", error);
        throw error;
      }
    }
  }

  /**
   * 获取TTS端点
   */
  private async getEndpoint(): Promise<TTSEndpoint> {
    const endpointUrl = "https://dev.microsofttranslator.com/apps/endpoint?api-version=1.0";
    const headers = {
      "Accept-Language": "zh-Hans",
      "X-ClientVersion": "4.0.530a 5fe1dc6c",
      "X-UserId": "0f04d16a175c411e",
      "X-HomeGeographicRegion": "zh-Hans-CN",
      "X-ClientTraceId": clientId || "76a75279-2ffa-4c3d-8db8-7b47252aa41c",
      "X-MT-Signature": await this.generateSignature(endpointUrl),
      "User-Agent": "okhttp/4.5.0",
      "Content-Type": "application/json; charset=utf-8",
      "Accept-Encoding": "gzip"
    };

    const response = await fetch(endpointUrl, {
      method: "POST",
      headers: headers
    });

    if (!response.ok) {
      throw new Error(`获取端点失败: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * 生成签名
   */
  private async generateSignature(urlStr: string): Promise<string> {
    try {
      const url = urlStr.split("://")[1];
      const encodedUrl = encodeURIComponent(url);
      const uuidStr = crypto.randomUUID ? crypto.randomUUID().replace(/-/g, "") : Math.random().toString(36).substring(2, 15);
      const formattedDate = this.formatDate();
      const bytesToSign = `MSTranslatorAndroidApp${encodedUrl}${formattedDate}${uuidStr}`.toLowerCase();

      // 导入签名密钥
      const keyData = this.base64ToArrayBuffer("oik6PdDdMnOXemTbwvMn9de/h9lFnfBaCWbGMMZqqoSaQaqUOqjVGm5NqsmjcBI1x+sS9ugjB55HEJWRiFXYFw==");
      const key = await crypto.subtle.importKey(
        'raw',
        keyData,
        { name: 'HMAC', hash: { name: 'SHA-256' } },
        false,
        ['sign']
      );

      // 签名数据
      const signature = await crypto.subtle.sign(
        'HMAC',
        key,
        new TextEncoder().encode(bytesToSign)
      );

      // 转换为base64
      const signatureBase64 = this.arrayBufferToBase64(signature);
      return `MSTranslatorAndroidApp::${signatureBase64}::${formattedDate}::${uuidStr}`;
    } catch (error) {
      console.error("生成签名失败:", error);
      throw error;
    }
  }

  /**
   * 格式化日期
   */
  private formatDate(): string {
    const date = new Date();
    const utcString = date.toUTCString().replace(/GMT/, "").trim() + " GMT";
    return utcString.toLowerCase();
  }

  /**
   * Base64转ArrayBuffer
   */
  private base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binary_string = atob(base64);
    const len = binary_string.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++) {
      bytes[i] = binary_string.charCodeAt(i);
    }
    return bytes.buffer;
  }

  /**
   * ArrayBuffer转Base64
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    let binary = '';
    const bytes = new Uint8Array(buffer);
    const len = bytes.byteLength;
    for (let i = 0; i < len; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  /**
   * 生成占位符音频
   */
  private generatePlaceholderAudio(text: string): string {
    // 生成简单的占位符音频（静音MP3）
    const placeholderBase64 = "SUQzBAAAAAAAI1RTU0UAAAAPAAADTGF2ZjU4Ljc2LjEwMAAAAAAAAAAAAAAA//tQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWGluZwAAAA8AAAAEAAABIADAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1dXV6urq6urq6urq6urq6urq6urq6urq6urq6v////////////////////////////////8AAAAATGF2YzU4LjEzAAAAAAAAAAAAAAAAJAAAAAAAAAAAASDs90hvAAAAAAAAAAAAAAAAAAAA//tQxAAAAAAAAAAAAAAAAAAAAAAASW5mbwAAAA8AAAAEAAABIADAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1dXV6urq6urq6urq6urq6urq6urq6urq6urq6v////////////////////////////////8AAAAATGF2YzU4LjEzAAAAAAAAAAAAAAAAJAAAAAAAAAAAASDs90hvAAAAAAAAAAAAAAAAAAAA";
    return `data:audio/mpeg;base64,${placeholderBase64}`;
  }
}
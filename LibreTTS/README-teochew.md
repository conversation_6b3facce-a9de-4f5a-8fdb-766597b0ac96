# 潮汕话TTS集成说明

## 功能概述

本项目已成功集成潮汕话TTS功能，基于项目中的`teochew_wild`数据集，为用户提供潮汕话语音合成服务。

## 新增功能

### 1. 潮汕话TTS API
- **端点**: `/api/teochew-tts`
- **方法**: GET, POST
- **功能**: 将潮汕话文本转换为语音

### 2. 潮汕话说话人API  
- **端点**: `/api/teochew-voices`
- **方法**: GET
- **功能**: 获取可用的潮汕话说话人列表

### 3. 前端集成
- 在主界面的API选择中添加"潮汕话 TTS"选项
- 支持潮汕话汉字和拼音输入
- 提供专门的测试页面

## 使用方法

### 1. 在主界面使用
1. 打开 LibreTTS 主页面
2. 在"选择API"下拉菜单中选择"潮汕话 TTS (实验性)"
3. 选择合适的说话人
4. 输入潮汕话文本或拼音
5. 点击"生成语音"

### 2. 使用专门测试页面
访问 `teochew-test.html` 页面，该页面提供：
- 潮汕话文本输入
- 说话人选择
- 语速和音调调节
- 示例文本快速输入
- API连接测试

## 支持的输入格式

### 1. 潮汕话汉字
```
你好世界
潮汕话好听
今日天气好
```

### 2. 潮汕话拼音
```
le2 ho2 si3 gai3
dio5 suan1 uê7 ho2 tian1
gim1 rig8 tin1 ki3 ho2
```

### 3. 混合输入
```
你好 le2 ho2
潮汕话 dio5 suan1 uê7
```

## API 使用示例

### POST 请求
```javascript
fetch('/api/teochew-tts', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    text: "你好世界",
    voice: "S001",
    rate: 0,
    pitch: 0
  })
})
.then(response => response.blob())
.then(blob => {
  const audioUrl = URL.createObjectURL(blob);
  // 播放或下载音频
});
```

### GET 请求
```
/api/teochew-tts?t=你好世界&v=S001&r=0&p=0
```

### 获取说话人列表
```javascript
fetch('/api/teochew-voices')
.then(response => response.json())
.then(speakers => {
  console.log(speakers);
  // {"S001": "潮汕话-男声1", "S002": "潮汕话-女声1", ...}
});
```

## 可用说话人

目前提供10个说话人选项：
- S001: 潮汕话-男声1
- S002: 潮汕话-女声1  
- S003: 潮汕话-男声2
- S004: 潮汕话-女声2
- S005: 潮汕话-男声3
- S006: 潮汕话-女声3
- S007: 潮汕话-男声4
- S008: 潮汕话-女声4
- S009: 潮汕话-男声5
- S010: 潮汕话-女声5

## 技术特点

### 1. 文本处理
- 自动检测潮汕话文本
- 支持汉字到拼音转换
- 集成 pyPengIm 文本处理工具

### 2. 语音合成
- 基于 teochew_wild 数据集
- 支持语速和音调调节
- 输出高质量WAV格式音频

### 3. 用户体验
- 简洁直观的界面
- 实时错误提示
- 音频预览和下载

## 部署说明

### 1. 文件结构
```
api/
├── teochew-tts.js      # 潮汕话TTS主API
├── teochew-voices.js   # 说话人列表API
├── tts.js              # 原有TTS API
└── voices.js           # 原有语音列表API

teochew_wild/           # 潮汕话数据集
├── annotation.txt      # 标注文件
├── README.md          # 数据集说明
└── ...

teochew-test.html      # 潮汕话测试页面
speakers.json          # 更新后的说话人配置
```

### 2. 配置文件更新
- `vercel.json`: 添加新的API路由
- `_routes.json`: 添加Cloudflare Pages路由
- `speakers.json`: 添加潮汕话说话人配置

## 注意事项

### 1. 当前状态
- 这是实验性功能，正在持续完善中
- TTS引擎需要进一步优化
- 文本处理功能需要完整的pyPengIm集成

### 2. 性能考虑
- 语音生成可能需要较长时间
- 建议使用较短的文本进行测试
- 大文本会自动分段处理

### 3. 兼容性
- 支持现代浏览器
- 需要JavaScript支持
- 音频播放需要浏览器支持

## 后续开发计划

1. **完善TTS引擎**: 集成实际的潮汕话语音合成模型
2. **优化文本处理**: 完整集成pyPengIm服务
3. **扩展说话人**: 利用完整数据集训练更多说话人
4. **提升音质**: 使用更先进的神经网络模型
5. **增强功能**: 添加情感控制、韵律调节等功能

## 反馈和支持

如果您在使用过程中遇到问题或有改进建议，请：
1. 查看浏览器控制台的错误信息
2. 尝试使用测试页面进行调试
3. 检查输入文本格式是否正确
4. 确认网络连接正常

## 相关资源

- [Teochew-Wild数据集](./teochew_wild/README.md)
- [pyPengIm文本处理工具](https://github.com/p1an-lin-jung/teochew-g2p)
- [潮汕话拼音方案说明](./teochew_wild/README.md#标注文件格式)
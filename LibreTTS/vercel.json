{"version": 2, "rewrites": [{"source": "/api/tts", "destination": "/api/tts.js"}, {"source": "/api/voices", "destination": "/api/voices.js"}, {"source": "/api/teochew-tts", "destination": "/api/teochew-tts.js"}, {"source": "/api/teochew-voices", "destination": "/api/teochew-voices.js"}], "headers": [{"source": "/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, x-auth-token"}]}]}
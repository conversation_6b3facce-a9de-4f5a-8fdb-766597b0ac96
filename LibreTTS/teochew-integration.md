# 潮汕话TTS集成方案

## 概述
本方案将潮汕话TTS功能集成到现有的LibreTTS系统中，利用项目中的teochew_wild数据集，为用户提供潮汕话语音合成服务。

## 集成架构

### 1. 后端API扩展
- **新增API端点**：
  - `/api/teochew-tts` - 潮汕话文本转语音
  - `/api/teochew-voices` - 获取潮汕话说话人列表

### 2. 数据集利用
- 使用`teochew_wild/`目录下的潮汕话数据集
- 包含20个说话人，12,500条音频片段
- 支持正字和拼音双重标注

### 3. 文本处理流程
1. **文本检测**：识别输入是否为潮汕话
2. **拼音转换**：汉字转潮汕话拼音（集成pyPengIm）
3. **语音合成**：基于拼音生成音频
4. **音频输出**：返回WAV格式音频

## 技术实现

### 后端组件

#### 1. 潮汕话TTS API (`api/teochew-tts.js`)
```javascript
// 主要功能：
- 文本预处理和验证
- 潮汕话拼音转换
- 语音合成引擎调用
- 音频格式处理
```

#### 2. 说话人管理 (`api/teochew-voices.js`)
```javascript
// 主要功能：
- 从数据集提取说话人信息
- 提供说话人列表API
- 支持说话人筛选
```

### 前端集成

#### 1. 语音选择扩展
在`speakers.json`中添加潮汕话选项：
```json
{
  "teochew-api": {
    "speakers": {
      "S001": "潮汕话-男声1",
      "S002": "潮汕话-女声1",
      ...
    }
  }
}
```

#### 2. 用户界面更新
- 在语音选择下拉菜单中添加"潮汕话"选项
- 提供潮汕话文本输入提示
- 支持拼音和汉字混合输入

## 部署配置

### 1. Vercel配置更新
```json
// vercel.json
{
  "rewrites": [
    {
      "source": "/api/teochew-tts",
      "destination": "/api/teochew-tts.js"
    },
    {
      "source": "/api/teochew-voices", 
      "destination": "/api/teochew-voices.js"
    }
  ]
}
```

### 2. 路由配置更新
```json
// _routes.json
{
  "routes": [
    { 
      "pattern": "/api/teochew-tts", 
      "function": "api/teochew-tts",
      "methods": ["GET", "POST", "OPTIONS"]
    },
    { 
      "pattern": "/api/teochew-voices", 
      "function": "api/teochew-voices",
      "methods": ["GET", "OPTIONS"] 
    }
  ]
}
```

## 使用方式

### 1. API调用示例

#### POST请求
```javascript
fetch('/api/teochew-tts', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    text: "你好世界", // 潮汕话文本或拼音
    voice: "S001",    // 说话人ID
    rate: 0,          // 语速调节
    pitch: 0          // 音调调节
  })
})
```

#### GET请求
```
/api/teochew-tts?t=你好世界&v=S001&r=0&p=0
```

### 2. 获取说话人列表
```javascript
fetch('/api/teochew-voices')
  .then(response => response.json())
  .then(speakers => {
    // speakers: {"S001": "潮汕话-男声1", ...}
  })
```

## 扩展功能

### 1. 文本处理增强
- **pyPengIm集成**：完整的潮汕话文本处理
- **多音字处理**：智能选择正确读音
- **方言转换**：支持不同地区口音

### 2. 语音质量优化
- **VITS模型**：使用先进的神经网络TTS
- **情感控制**：支持不同语音风格
- **韵律调节**：更自然的语音表达

### 3. 用户体验提升
- **实时预览**：边输入边试听
- **历史记录**：保存常用潮汕话短语
- **拼音显示**：显示对应的潮汕话拼音

## 技术栈

### 核心技术
- **后端**：Node.js + Vercel Functions
- **TTS引擎**：待集成（VITS/其他开源方案）
- **文本处理**：pyPengIm（潮汕话G2P）
- **数据集**：Teochew-Wild

### 依赖服务
- **拼音转换**：pyPengIm服务
- **语音合成**：TTS模型服务
- **音频处理**：FFmpeg（如需格式转换）

## 部署步骤

1. **部署API**：上传新的API文件到Vercel
2. **更新配置**：修改路由和speakers配置
3. **集成TTS引擎**：部署实际的语音合成服务
4. **测试验证**：确保API正常工作
5. **前端更新**：更新用户界面

## 注意事项

### 1. 性能考虑
- 语音合成可能需要较长时间
- 考虑添加队列机制处理并发请求
- 实现音频缓存减少重复计算

### 2. 资源限制
- Vercel Functions有执行时间限制
- 考虑使用外部TTS服务
- 优化音频文件大小

### 3. 用户体验
- 提供清晰的使用说明
- 支持拼音输入提示
- 错误处理和用户反馈

## 后续开发

1. **完善TTS引擎**：集成实际的潮汕话语音合成模型
2. **优化文本处理**：完整集成pyPengIm服务
3. **扩展说话人**：利用完整的数据集训练更多说话人
4. **移动端适配**：优化移动设备上的使用体验
5. **API文档**：提供完整的API使用文档
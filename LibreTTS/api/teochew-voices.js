// 潮汕话语音列表API
import { getTeochewSpeakers } from './teochew-tts.js';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, x-auth-token");
  
  // Handle OPTIONS request
  if (req.method === "OPTIONS") {
    return res.status(204).end();
  }
  
  // Only allow GET requests
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }
  
  try {
    const speakers = await getTeochewSpeakers();
    return res.json(speakers);
  } catch (error) {
    console.error("API Error:", error);
    return res.status(500).json({ error: error.message || "Failed to fetch Teochew voices" });
  }
}
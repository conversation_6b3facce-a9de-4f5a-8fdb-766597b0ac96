// 潮汕话TTS API
import fs from 'fs';
import path from 'path';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, x-auth-token");
  
  // Handle OPTIONS request
  if (req.method === "OPTIONS") {
    return res.status(204).end();
  }

  try {
    if (req.method === "POST") {
      const body = req.body;
      const text = body.text || "";
      const speaker = body.voice || "S001"; // 默认使用S001说话人
      const rate = Number(body.rate) || 0;
      const pitch = Number(body.pitch) || 0;
      
      return await handleTeochewTTS(res, text, speaker, rate, pitch);
    } else if (req.method === "GET") {
      const { query } = req;
      const text = query.t || "";
      const speaker = query.v || "S001";
      const rate = Number(query.r) || 0;
      const pitch = Number(query.p) || 0;
      
      return await handleTeochewTTS(res, text, speaker, rate, pitch);
    } else {
      return res.status(405).json({ error: "Method not allowed" });
    }
  } catch (error) {
    console.error("Teochew TTS API Error:", error);
    return res.status(500).json({ error: error.message || "Internal Server Error" });
  }
}

async function handleTeochewTTS(res, text, speaker, rate, pitch) {
  try {
    // 检查文本是否为潮汕话
    if (!isTeochewText(text)) {
      return res.status(400).json({ 
        error: "输入文本不是潮汕话，请使用潮汕话文本或拼音" 
      });
    }

    // 文本预处理 - 转换为潮汕话拼音
    const teochewPinyin = await convertToTeochewPinyin(text);
    
    // 生成语音 - 这里需要集成实际的潮汕话TTS引擎
    const audioBuffer = await generateTeochewAudio(teochewPinyin, speaker, rate, pitch);
    
    // 设置响应头
    res.setHeader("Content-Type", "audio/wav");
    res.setHeader("Content-Disposition", `attachment; filename="teochew_${speaker}.wav"`);
    
    return res.send(audioBuffer);
  } catch (error) {
    console.error("Teochew TTS Error:", error);
    return res.status(500).json({ error: error.message });
  }
}

// 检查是否为潮汕话文本
function isTeochewText(text) {
  // 简单的潮汕话检测逻辑
  // 可以检查是否包含潮汕话特有的字符或拼音
  const teochewPatterns = [
    /[呾乜𠀾𡛂衹]/,  // 潮汕话特有字符
    /\b[a-z]+[1-8]\b/g,  // 潮汕话拼音声调模式
  ];
  
  return teochewPatterns.some(pattern => pattern.test(text));
}

// 转换为潮汕话拼音
async function convertToTeochewPinyin(text) {
  // 这里需要集成潮汕话文本处理工具 pyPengIm
  // 暂时返回原文本，实际应该调用文本转拼音服务
  
  // 如果已经是拼音格式，直接返回
  if (/\b[a-z]+[1-8]\b/g.test(text)) {
    return text;
  }
  
  // 否则需要进行汉字到拼音的转换
  // 这里应该调用 pyPengIm 或类似的服务
  return await callPyPengIm(text);
}

// 调用pyPengIm进行文本转换
async function callPyPengIm(text) {
  // 这里应该集成实际的pyPengIm服务
  // 暂时返回示例转换
  console.log("Converting to Teochew pinyin:", text);
  
  // 示例：简单的字符映射
  const charMap = {
    '你': 'le2',
    '好': 'ho2', 
    '世': 'si3',
    '界': 'gai3',
    '潮': 'dio5',
    '汕': 'suan1',
    '话': 'uê7'
  };
  
  let result = '';
  for (let char of text) {
    if (charMap[char]) {
      result += charMap[char] + ' ';
    } else {
      result += char + ' ';
    }
  }
  
  return result.trim();
}

// 生成潮汕话音频
async function generateTeochewAudio(pinyin, speaker, rate, pitch) {
  // 这里需要集成实际的潮汕话TTS引擎
  // 可能的选择：
  // 1. 基于现有的潮汕话数据集训练的模型
  // 2. 使用VITS或其他开源TTS框架
  // 3. 调用第三方潮汕话TTS服务
  
  console.log(`Generating Teochew audio for: ${pinyin}, speaker: ${speaker}`);
  
  // 暂时返回一个空的音频buffer作为占位符
  // 实际实现中应该调用TTS引擎
  const dummyAudioBuffer = Buffer.alloc(1024, 0);
  
  return dummyAudioBuffer;
}

// 获取可用的潮汕话说话人列表
export async function getTeochewSpeakers() {
  try {
    // 从teochew_wild数据集中提取说话人信息
    const annotationPath = path.join(process.cwd(), 'teochew_wild', 'annotation.txt');
    
    if (fs.existsSync(annotationPath)) {
      const content = fs.readFileSync(annotationPath, 'utf-8');
      const speakers = new Set();
      
      // 解析annotation文件获取说话人ID
      const lines = content.split('\n');
      for (const line of lines) {
        if (line.trim()) {
          const parts = line.split('|');
          if (parts.length >= 2) {
            const speaker = parts[1].trim();
            speakers.add(speaker);
          }
        }
      }
      
      // 转换为说话人对象
      const speakerList = {};
      speakers.forEach(speaker => {
        speakerList[speaker] = `潮汕话-${speaker}`;
      });
      
      return speakerList;
    }
  } catch (error) {
    console.error('Error loading Teochew speakers:', error);
  }
  
  // 返回默认说话人列表
  return {
    'S001': '潮汕话-男声1',
    'S002': '潮汕话-女声1', 
    'S003': '潮汕话-男声2',
    'S004': '潮汕话-女声2'
  };
}
---
extra_gated_fields:
  Name: text
  Email: text
  Profession: text
  Position: text
  Organization/Institution: text
  I agree to the Terms of Access: checkbox
task_categories:
- text-to-speech
- automatic-speech-recognition
size_categories:
- 10K<n<100K
license: cc-by-4.0
language:
- zh
pretty_name: Teochew-Wild
---

## Teochew-Wild：首个正字标注的野外潮州话数据集

本数据集（Teochew-Wild）是从网络上发音清晰、噪声较少的音视频内容中获取的，原始音视频的数据来源为：民生新闻、潮汕讲古、地方电视节目、故事书、抖音自媒体口播等，我借鉴了[Emilla](https://github.com/open-mmlab/Amphion/tree/main/preprocessors/Emilia)提出的数据集自动处理流水线，对原始数据进行归一化、降噪和剪切（部分自动剪切效果差的使用手工修正）； 

Teochew-Wild总共包括20个发音标准、念错率低的潮汕母语说话人、共12500条音频片段，包含潮州市区、汕头市区、澄海、榕江音、潮安南部等多个区域的口音，语料内容覆盖书面用语与口头用语，并同时提供正字和拼音标注，是首个公开可用、标注准确率高的潮州话数据集，主要面向**语音识别和语音合成**任务。

## pyPengIm 文本处理工具

[pyPengIm](https://github.com/p1an-lin-jung/teochew-g2p)是潮汕话语音合成（text-to-speech，TTS）的铺垫工作，用于潮汕话的文本端处理，具有以下功能：

1、从汉字输入，并输出对应的潮汕话拼音，默认情况下输出常用、高频的读音，也支持输出所有的读音。

2、具有分词功能和多音字消歧功能。

3、默认以潮州市区的府城音为标准，并支持将其转换到汕头市区音、揭阳市区音、澄海口音等。

4、支持将普通话的词汇，转为更地道的潮汕话口语表达。

5、支持将潮州拼音转换到国际音标（IPA），或者拆分成【声母+韵母】的格式。

### 更新日志
- **2025/04/28**: 修改了由于OCR错误引发的拼音标注的错误（客[keh]->[kêh4]））；修改 【𠶧】，将其分为【昣@diang1】和【倎@diang5】，分别表示“时候”、“谁”；修改S010说话人（庵埠）的闭口音为开口音（[-Xm]->[-Xng]、[-Xb]->[-Xg]）
- **2025/03/22**: 修改了部分拼音标注的错误（侍、抚、暝暗等）；修改了S001F008C068.wav的音频错误；调整了压缩包的文件夹格式
- **2025/03/21**: Teochow-Wild 论文正式被 ICME 2025 接收


### 元数据
| 属性             | 值                          |
|------------------|-----------------------------|
| 通道数           | 1                           |
| 采样率           | 22050 Hz                    |
| 编码格式         | 16-bit Signed Integer PCM   |
| 文件格式         | wav                         |
| 说话人数量       | 20                          |
| 性别比           | 11：9                       |
| 总时长           | 18.87 小时                  |
| 音频数量         | 12500                       |
| 平均时长         | 5.43 秒                     |
| 总字数           | 274020                      |
| 标注方式         | 汉字正字+潮州拼音           |
| DNSMOS值         | 3.12 ± 0.32                 |


### 文件结构

```
- S001(Speaker)
  - S001F001(File)
    - S001F001C000.wav(Clip)
  - S001F002
    - S001F002C000.wav
- S002
  - S002F001
    - S002F001C000.wav
```

### 标注文件格式

汉字部分以 **[歹看正字法（PKO）](https://github.com/p1an-lin-jung/teochew-g2p/blob/master/doc/readme.md)** 标注，拼音部分以潮州拼音（Teochew Pinyin）标注，一般情况下只标注原调，不标注变调（除非该说话片段的变调较特殊，按变调标）；说话人念错发音，则按错音标注。

```
<wav_path>|<speaker>|<Chinese Character>|<Teochew Pinyin>
S001/S001F001/S001F001C001.wav|S001|伊祇个人|i1 zi2 gai5 nang5
```


原始标注文件(raw_annotation)，主要是方便检查标注的错误，格式如下：
```
<wav_path>| <Chinese Character>@<Teochew Pinyin> <Chinese Character>@<Teochew Pinyin> ...
S001/S001F001/S001F001C001.wav|伊@i1 祇@zi2 个@gai5 人@nang5
```


### 引用

如果teochew-wild数据集或pyPengIm文本工具对您有所帮助，请引用我的论文：

```bibtex
@article{pan2025teochew,
  title={Teochew-Wild: The First In-the-wild Teochew Dataset with Orthographic Annotations},
  author={Pan, Linrong and Jiang, Chenglong and Hou, Gaoze and Gao, Ying},
  journal={arXiv preprint arXiv:2505.05056},
  year={2025}
}
```



### 注意
作者不拥有 Teochew-Wild 中音频文件的版权，版权归视频或音频的原始所有者所有；

作者对数据集不作任何代表或保证，包括但不限于对非侵权或适用于特定用途的保证；

用户对数据集的使用行为承担全部责任；

根据 CC BY-NC-4.0 许可证，用户仅允许将 Teochew-Wild 数据集用于非商业目的。
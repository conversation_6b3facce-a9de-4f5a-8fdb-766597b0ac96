<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>潮汕话TTS测试页面</title>
    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .teochew-examples {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .example-text {
            cursor: pointer;
            padding: 5px 10px;
            margin: 5px;
            background-color: #e9ecef;
            border-radius: 4px;
            display: inline-block;
        }
        .example-text:hover {
            background-color: #dee2e6;
        }
        .pinyin-display {
            font-family: monospace;
            background-color: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h2 class="text-center mb-0">潮汕话TTS测试</h2>
                    </div>
                    <div class="card-body">
                        <!-- 说明文档 -->
                        <div class="alert alert-info">
                            <h5>使用说明：</h5>
                            <ul>
                                <li>支持潮汕话汉字和拼音输入</li>
                                <li>拼音格式：如 "le2 ho2"（你好）</li>
                                <li>点击下方示例可快速填入文本</li>
                                <li>目前为实验性功能，正在完善中</li>
                            </ul>
                        </div>

                        <!-- 文本输入 -->
                        <div class="form-group">
                            <label for="teochewText">潮汕话文本：</label>
                            <textarea id="teochewText" class="form-control" rows="4" 
                                placeholder="请输入潮汕话文本或拼音，例如：你好世界 或 le2 ho2 si3 gai3"></textarea>
                        </div>

                        <!-- 说话人选择 -->
                        <div class="form-group">
                            <label for="teochewSpeaker">选择说话人：</label>
                            <select id="teochewSpeaker" class="form-control">
                                <option value="S001">潮汕话-男声1</option>
                                <option value="S002">潮汕话-女声1</option>
                                <option value="S003">潮汕话-男声2</option>
                                <option value="S004">潮汕话-女声2</option>
                            </select>
                        </div>

                        <!-- 参数调节 -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="teochewRate">语速：<span id="rateValue">0</span></label>
                                    <input type="range" class="form-control-range" id="teochewRate" 
                                           min="-50" max="50" value="0" step="5">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="teochewPitch">音调：<span id="pitchValue">0</span></label>
                                    <input type="range" class="form-control-range" id="teochewPitch" 
                                           min="-50" max="50" value="0" step="5">
                                </div>
                            </div>
                        </div>

                        <!-- 生成按钮 -->
                        <button type="button" class="btn btn-primary btn-block" id="generateTeochew">
                            生成潮汕话语音
                        </button>

                        <!-- 音频播放器 -->
                        <div id="audioContainer" class="mt-3" style="display: none;">
                            <audio id="audioPlayer" controls class="w-100"></audio>
                            <a id="downloadLink" class="btn btn-success btn-block mt-2" href="#" download="teochew.wav">
                                下载音频文件
                            </a>
                        </div>

                        <!-- 加载状态 -->
                        <div id="loadingIndicator" class="text-center mt-3" style="display: none;">
                            <div class="spinner-border" role="status">
                                <span class="sr-only">生成中...</span>
                            </div>
                            <p class="mt-2">正在生成潮汕话语音，请稍候...</p>
                        </div>

                        <!-- 拼音显示 -->
                        <div id="pinyinDisplay" class="pinyin-display" style="display: none;">
                            <strong>转换后的拼音：</strong>
                            <div id="pinyinText"></div>
                        </div>

                        <!-- 示例文本 -->
                        <div class="teochew-examples">
                            <h5>示例文本（点击使用）：</h5>
                            <div class="example-text" data-text="你好世界">你好世界</div>
                            <div class="example-text" data-text="潮汕话好听">潮汕话好听</div>
                            <div class="example-text" data-text="le2 ho2 si3 gai3">le2 ho2 si3 gai3</div>
                            <div class="example-text" data-text="dio5 suan1 uê7 ho2 tian1">dio5 suan1 uê7 ho2 tian1</div>
                            <div class="example-text" data-text="今日天气好">今日天气好</div>
                            <div class="example-text" data-text="食饭了无">食饭了无</div>
                        </div>

                        <!-- API状态 -->
                        <div class="mt-3">
                            <button type="button" class="btn btn-outline-info btn-sm" id="testApi">
                                测试API连接
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="loadSpeakers">
                                刷新说话人列表
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            // 更新滑块显示值
            $('#teochewRate').on('input', function() {
                $('#rateValue').text($(this).val());
            });
            
            $('#teochewPitch').on('input', function() {
                $('#pitchValue').text($(this).val());
            });

            // 示例文本点击事件
            $('.example-text').click(function() {
                const text = $(this).data('text');
                $('#teochewText').val(text);
            });

            // 生成语音
            $('#generateTeochew').click(function() {
                const text = $('#teochewText').val().trim();
                if (!text) {
                    alert('请输入要转换的文本');
                    return;
                }

                const speaker = $('#teochewSpeaker').val();
                const rate = $('#teochewRate').val();
                const pitch = $('#teochewPitch').val();

                generateTeochewAudio(text, speaker, rate, pitch);
            });

            // 测试API连接
            $('#testApi').click(function() {
                testTeochewApi();
            });

            // 刷新说话人列表
            $('#loadSpeakers').click(function() {
                loadTeochewSpeakers();
            });

            // 页面加载时获取说话人列表
            loadTeochewSpeakers();
        });

        // 生成潮汕话音频
        function generateTeochewAudio(text, speaker, rate, pitch) {
            $('#loadingIndicator').show();
            $('#audioContainer').hide();
            $('#generateTeochew').prop('disabled', true);

            const requestData = {
                text: text,
                voice: speaker,
                rate: parseInt(rate),
                pitch: parseInt(pitch)
            };

            fetch('/api/teochew-tts', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => {
                        throw new Error(err.error || '生成失败');
                    });
                }
                return response.blob();
            })
            .then(blob => {
                const audioUrl = URL.createObjectURL(blob);
                $('#audioPlayer').attr('src', audioUrl);
                $('#downloadLink').attr('href', audioUrl);
                $('#audioContainer').show();
                
                // 显示成功消息
                showMessage('语音生成成功！', 'success');
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('生成失败：' + error.message, 'danger');
            })
            .finally(() => {
                $('#loadingIndicator').hide();
                $('#generateTeochew').prop('disabled', false);
            });
        }

        // 加载说话人列表
        function loadTeochewSpeakers() {
            fetch('/api/teochew-voices')
            .then(response => response.json())
            .then(speakers => {
                const select = $('#teochewSpeaker');
                select.empty();
                
                Object.keys(speakers).forEach(speakerId => {
                    select.append(new Option(speakers[speakerId], speakerId));
                });
                
                showMessage('说话人列表加载成功', 'info');
            })
            .catch(error => {
                console.error('Error loading speakers:', error);
                showMessage('加载说话人列表失败', 'warning');
            });
        }

        // 测试API连接
        function testTeochewApi() {
            fetch('/api/teochew-voices')
            .then(response => {
                if (response.ok) {
                    showMessage('API连接正常', 'success');
                } else {
                    showMessage('API连接失败', 'danger');
                }
            })
            .catch(error => {
                showMessage('API连接错误：' + error.message, 'danger');
            });
        }

        // 显示消息
        function showMessage(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            `;
            
            // 移除旧的消息
            $('.alert').remove();
            
            // 添加新消息
            $('.card-body').prepend(alertHtml);
            
            // 3秒后自动消失
            setTimeout(() => {
                $('.alert').alert('close');
            }, 3000);
        }
    </script>
</body>
</html>
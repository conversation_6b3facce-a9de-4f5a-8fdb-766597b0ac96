# LibreTTS 项目分析摘要

## 核心功能

*   一个名为LibreTTS的在线文本转语音（TTS）工具。
*   支持多种语言和声音。
*   允许用户调整语速和语调。
*   提供音频的实时预览和下载。
*   具有历史记录功能。
*   一个关键特性是支持添加自定义的TTS API，兼容OpenAI和Microsoft Edge的API格式。

## 技术栈

*   **前端:** HTML, CSS, JavaScript (使用Bootstrap和jQuery)。
*   **后端/API:** 使用Vercel Serverless Functions实现。API端点在`api/`目录下定义。
*   **部署:** 主要通过Vercel部署，也支持Cloudflare Pages。`vercel.json`文件定义了路由重写和头部信息。

## 主要文件分析

*   `README.md`: 提供了非常全面的项目介绍、功能列表、API说明和部署指南。
*   `index.html`: 定义了应用的前端界面，包括文本输入框、选项（API、声音、语速、语调）、按钮（生成、预览、下载）、历史记录区域以及用于管理自定义API的模态框。它还包含了一个用于密码验证的模态框。
*   `script.js`: 包含了前端的主要逻辑。它处理用户交互，调用TTS API，管理音频播放和下载，并实现了自定义API的管理功能（添加、删除、导入、导出）。它还负责根据所选API动态更新UI。
*   `api/tts.js`: 这是核心的Edge TTS API的后端实现。它接收来自前端的请求，然后向Microsoft的TTS服务发出请求以生成音频。它还处理了获取和刷新认证token的逻辑。
*   `vercel.json`: 配置了Vercel的部署设置，主要是将API请求（如`/api/tts`）重写到相应的serverless function文件（如`/api/tts.js`）。

## 潜在的复杂性/特点

*   **自定义API管理:** 这是项目的一个核心高级功能，允许用户极大地扩展其功能。`script.js`中有大量代码用于处理这部分逻辑。
*   **密码保护:** 项目支持通过设置`PASSWORD`环境变量来启用访问密码。`index.html`和`api/check-password.js`, `api/verify-password.js`共同实现了这个功能。
*   **长文本处理:** `script.js`中提到了长文本的自动分段处理，这是一个重要的用户体验优化。
*   **潮州话支持:** `teochew-wild/`目录和相关的API文件表明项目对潮州话有特殊的集成。

## 结论

这是一个功能相对完善且考虑周全的前端应用，通过Vercel Serverless Functions提供后端API服务。其最大的亮点是高度的可扩展性，允许用户集成自己的TTS服务。代码结构清晰，前端逻辑主要集中在`script.js`，后端API则按功能拆分到不同的文件中。

---

# `teochew_wild` 数据集分析摘要

## 项目目标

*   `Teochew-Wild` 是一个为潮州话设计的野外语音数据集。
*   主要用于**语音识别 (ASR)** 和**语音合成 (TTS)** 任务。
*   号称是首个公开的、标注准确率高的潮州话数据集。

## 数据来源与处理

*   数据来源于网络上的音视频，包括新闻、讲古、电视节目等。
*   使用了自动处理流水线对数据进行归一化、降噪和剪切。

## 数据集规模

*   包含20个说话人。
*   共12500条音频片段。
*   总时长约18.87小时。
*   覆盖多种潮州话口音。

## 标注信息

*   `annotation.txt` 文件是主要的标注文件。
*   格式为：`<wav_path>|<speaker>|<Chinese Character>|<Teochew Pinyin>`。
*   汉字部分使用“歹看正字法（PKO）”进行标注。
*   拼音部分使用潮州拼音（Teochew Pinyin）标注。
*   `raw_annotation.txt` 提供了另一种格式的原始标注，方便检查。

## 说话人信息

*   `speaker_info.csv` 文件包含了20个说话人的详细信息。
*   信息包括ID、性别、口音/地区、描述、身份、年龄段和来源。

## 相关工具

*   `README.md` 提到了一个名为 `pyPengIm` 的文本处理工具，用于潮州话的文本端处理，包括汉字转拼音、分词、多音字消歧等功能。

## 结论

`teochew_wild` 是一个结构良好、标注详细的潮州话语音数据集，为相关的ASR和TTS研究提供了宝贵的资源。它与主项目 `LibreTTS` 的关系在于，它可以作为训练潮州话TTS模型的数据源，从而增强 `LibreTTS` 的潮州话语音合成能力。`api/teochew-tts.js` 和 `api/teochew-voices.js` 很可能就是利用这个数据集或基于它训练的模型来实现的。